<script lang="ts" setup>
import AccountSuccess from "~/assets/images/general/create-account-success.png";
import Logo from "~/assets/images/general/logo.png";
useHead({
  title: "Create Account Success",
});
</script>

<template>
  <div class="flex h-screen flex-col items-center justify-center">
    <img :src="Logo" alt="logo" class="h-16" />
    <img :src="AccountSuccess" alt="logo" class="-mt-12 w-[30%]" />
    <div class="text-primary mt-4 text-2xl font-bold">
      Create Account Success
    </div>
    <div class="mt-2 text-sm text-gray-500">
      Check your email for account activation
    </div>
    <UButton to="/" class="mt-6" variant="outline">Back to Home</UButton>
  </div>
</template>
