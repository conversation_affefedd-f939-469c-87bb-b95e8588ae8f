<script lang="ts" setup>
import ActivationSuccess from "~/assets/images/general/activation-success.png";
import ActivationFailed from "~/assets/images/general/activation-failed.png";
import Logo from "~/assets/images/general/logo.png";

useHead({
  title: "Activation",
});

const route = useRoute();
const { doActivation } = useAuth();
const code = computed(() => route.query.code as string);

// Debug: Log the code value
console.log("Current URL:", window.location?.href);
console.log("Route query:", route.query);
console.log("Code value:", code.value);

const isSuccess = ref(false);

// Only call activation if code exists
const { data: activationResult } = await useAsyncData(
  "activation",
  () => {
    if (!code.value) {
      console.log("No code found, skipping activation");
      return Promise.resolve(null);
    }
    console.log("Calling activation with code:", code.value);
    return doActivation(code.value);
  },
  {
    watch: [code] // Re-run when code changes
  }
);

watch(activationResult, () => {
  if (activationResult.value) {
    isSuccess.value = true;
  }
});
</script>

<template>
  <div class="flex h-screen flex-col items-center justify-center">
    <!-- Debug info - remove this after debugging -->
    <div class="mb-4 rounded bg-gray-100 p-4 text-sm">
      <div><strong>Debug Info:</strong></div>
      <div>Code: {{ code || 'undefined' }}</div>
      <div>Route Query: {{ JSON.stringify(route.query) }}</div>
      <div>Activation Result: {{ activationResult || 'null' }}</div>
    </div>
    <img :src="Logo" alt="logo" class="h-16" />
    <img
      :src="isSuccess ? ActivationSuccess : ActivationFailed"
      alt="logo"
      :class="isSuccess ? '-mt-4 w-[420px]' : 'w-[300px]'"
    />
    <div class="text-primary mt-4 text-2xl font-bold">
      Activation {{ isSuccess ? "Success" : "Failed" }}
    </div>
    <div class="mt-2 text-sm text-gray-500">
      {{
        isSuccess
          ? "Thank you for register account in Nuxt Event."
          : "Confirmastion code is invalid."
      }}
    </div>
    <UButton to="/" class="mt-6" variant="outline">Back to Home</UButton>
  </div>
</template>
