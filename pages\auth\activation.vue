<script lang="ts" setup>
import ActivationSuccess from "~/assets/images/general/activation-success.png";
import ActivationFailed from "~/assets/images/general/activation-failed.png";
import Logo from "~/assets/images/general/logo.png";
useHead({
  title: "Activation",
});

const isSuccess = ref(false);
</script>

<template>
  <div class="flex h-screen flex-col items-center justify-center">
    <img :src="Logo" alt="logo" class="h-16" />
    <img
      :src="isSuccess ? ActivationSuccess : ActivationFailed"
      alt="logo"
      :class="isSuccess ? '-mt-4 w-[30%]' : 'w-[20%]'"
    />
    <div class="text-primary mt-4 text-2xl font-bold">
      Activation {{ isSuccess ? "Success" : "Failed" }}
    </div>
    <div class="mt-2 text-sm text-gray-500">
      {{
        isSuccess
          ? "Thank you for register account in Nuxt Event."
          : "Confirmastion code is invalid."
      }}
    </div>
    <UButton to="/" class="mt-6" variant="outline">Back to Home</UButton>
  </div>
</template>
