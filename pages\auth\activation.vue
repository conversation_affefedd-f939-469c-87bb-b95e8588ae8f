<script lang="ts" setup>
import ActivationSuccess from "~/assets/images/general/activation-success.png";
import ActivationFailed from "~/assets/images/general/activation-failed.png";
import Logo from "~/assets/images/general/logo.png";

useHead({
  title: "Activation",
});

const route = useRoute();
const { doActivation } = useAuth();
const code = route.query.code;

const isSuccess = ref(false);
const { data: activationResult } = await useAsyncData("activation", () =>
  doActivation(code as string),
);

watch(activationResult, () => {
  if (activationResult.value) {
    isSuccess.value = true;
  }
});
</script>

<template>
  <div class="flex h-screen flex-col items-center justify-center">
    <img :src="Logo" alt="logo" class="h-16" />
    <img
      :src="isSuccess ? ActivationSuccess : ActivationFailed"
      alt="logo"
      :class="isSuccess ? '-mt-4 w-[420px]' : 'w-[300px]'"
    />
    <div class="text-primary mt-4 text-2xl font-bold">
      Activation {{ isSuccess ? "Success" : "Failed" }}
    </div>
    <div class="mt-2 text-sm text-gray-500">
      {{
        isSuccess
          ? "Thank you for register account in Nuxt Event."
          : "Confirmastion code is invalid."
      }}
    </div>
    <UButton to="/" class="mt-6" variant="outline">Back to Home</UButton>
  </div>
</template>
