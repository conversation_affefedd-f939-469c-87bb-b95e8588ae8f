<script lang="ts" setup>
import ActivationSuccess from "~/assets/images/general/activation-success.png";
import ActivationFailed from "~/assets/images/general/activation-failed.png";
import Logo from "~/assets/images/general/logo.png";

useHead({
  title: "Activation",
});

const route = useRoute();
const { doActivation } = useAuth();
const code = computed(() => route.query.code as string);


// Only call activation if code exists
const { data: activationResult, pending } = await useAsyncData(
  "activation",
  () => {
    if (!code.value) {
      return Promise.resolve(null);
    }
    return doActivation(code.value);
  },
  {
    watch: [code],
    server
  }
);

const isSuccess = computed(() => activationResult.value);
console.log("activationResult", activationResult.value);
</script>

<template>
  <div class="flex h-screen flex-col items-center justify-center">
    <img :src="Logo" alt="logo" class="h-16" />

    <!-- Loading State -->
    <div v-if="pending" class="flex flex-col items-center">
      <div class="mt-8 h-12 w-12 animate-spin rounded-full border-4 border-gray-300 border-t-blue-600"></div>
      <div class="mt-4 text-lg font-medium text-gray-600">Activating your account...</div>
    </div>

    <!-- Result State -->
    <div v-else class="flex flex-col items-center">
      <img
        :src="isSuccess ? ActivationSuccess : ActivationFailed"
        alt="activation result"
        :class="isSuccess ? '-mt-4 w-[420px]' : 'w-[300px]'"
      />
      <div class="text-primary mt-4 text-2xl font-bold">
        Activation {{ isSuccess ? "Success" : "Failed" }}
      </div>
      <div class="mt-2 text-sm text-gray-500">
        {{
          isSuccess
            ? "Thank you for register account in Nuxt Event."
            : "Confirmation code is invalid."
        }}
      </div>
      <UButton to="/" class="mt-6" variant="outline">Back to Home</UButton>
    </div>
  </div>
</template>
