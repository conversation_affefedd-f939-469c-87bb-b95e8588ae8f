import type {
  LoginFormData,
  LoginRequest,
  LoginResponse,
  RegisterFormData,
  RegisterResponse,
} from "~/types/auth";

export const useAuth = () => {
  const { http } = useApi();

  const doRegister = async (
    formData: RegisterFormData,
  ): Promise<RegisterResponse> => {
    try {
      const response = await http.post<RegisterResponse>(
        "/auth/register",
        formData,
      );

      return response;
    } catch (error: any) {
      throw error;
    }
  };

  const doLogin = async (
    formData: LoginFormData,
  ): Promise<LoginResponse | null> => {
    try {
      // Prepare API request data
      const requestData: LoginRequest = {
        identifier: formData.email.trim().toLowerCase(),
        password: formData.password,
      };

      const response = await http.post<LoginResponse>(
        "/auth/login",
        requestData,
      );

      return response;
    } catch (error: any) {
      throw error;
    }
  };

  const doActivation = async (code: string): Promise<any> => {
    try {
      const response = await http.post("/auth/activation", { code });

      return response;
    } catch (error: any) {
      throw error;
    }
  };

  return {
    doRegister,
    doLogin,
    doActivation,
  };
};
