import type { ApisauceInstance } from "apisauce";
import {
  getApiClient,
  apiGet,
  apiPost,
  apiPut,
  apiDelete,
  handleApiError,
} from "~/utils/api";

/**
 * NUXT composable for API operations
 * Provides a convenient way to access the API client and common HTTP methods
 * throughout your NUXT application with proper SSR support
 */
export const useApi = () => {
  /**
   * Get the configured API client instance
   */
  const getClient = (): ApisauceInstance => {
    return getApiClient();
  };

  /**
   * Get the current API base URL from runtime config
   */
  const getBaseUrl = (): string => {
    const config = useRuntimeConfig();
    return config.public.apiBaseUrl;
  };

  /**
   * Convenient HTTP methods with built-in error handling
   */
  const http = {
    /**
     * GET request
     * @param url
     * @param params - Query parameters
     */
    get: async <T>(url: string, params?: object): Promise<T> => {
      return apiGet<T>(url, params);
    },

    /**
     * POST request
     * @param url
     * @param data - Request body data
     */
    post: async <T>(url: string, data?: any): Promise<T> => {
      return apiPost<T>(url, data);
    },

    /**
     * PUT request
     * @param url
     * @param data - Request body data
     */
    put: async <T>(url: string, data?: any): Promise<T> => {
      return apiPut<T>(url, data);
    },

    /**
     * DELETE request
     * @param url
     */
    delete: async <T>(url: string): Promise<T> => {
      return apiDelete<T>(url);
    },
  };

  /**
   * Raw API client for advanced usage
   * Use this when you need direct access to apisauce features
   * like custom headers, monitors, or transforms
   */
  const client = getClient();

  return {
    client,
    http,
    getBaseUrl,
    handleApiError,
  };
};
